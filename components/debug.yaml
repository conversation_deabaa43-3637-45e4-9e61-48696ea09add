logger:
  hardware_uart: USB_SERIAL_JTAG
  logs:
    sensor: WARN

debug:
  update_interval: 60s

sensor:
  - platform: internal_temperature
    id: esp_temperature
    name: 'ESP Temperature'
    entity_category: diagnostic
    icon: 'mdi:fire-alert'
    update_interval: 60s

  - platform: debug
    free:
      id: heap_free
      name: 'ESP Heap Free'
      entity_category: diagnostic
      icon: 'mdi:memory'
    psram:
      id: psram_free
      name: 'ESP PSRAM Free'
      entity_category: diagnostic
      icon: 'mdi:memory'
    cpu_frequency:
      id: cpu_frequency
      name: 'ESP CPU Frequency'
      entity_category: diagnostic
      icon: 'mdi:chip'

text_sensor:
  - platform: uptime
    id: esp_uptime
    name: 'ESP Uptime'
    entity_category: diagnostic
    icon: 'mdi:timer-outline'
    format:
      separator: ' '

  - platform: debug
    reset_reason:
      id: reset_reason
      name: 'ESP Reset Reason'
      entity_category: diagnostic
      icon: 'mdi:restart-alert'
