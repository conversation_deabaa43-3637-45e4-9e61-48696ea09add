esphome:
  name: voice-assistant-box
  friendly_name: 'Voice Assistant Box'
  name_add_mac_suffix: true
  platformio_options:
    board_build.flash_mode: dio

esp32:
  board: esp32-s3-devkitc-1
  flash_size: 16MB
  cpu_frequency: 240MHz
  framework:
    type: esp-idf
    sdkconfig_options:
      CONFIG_ESP32S3_DEFAULT_CPU_FREQ_240: 'y'
      CONFIG_ESP32S3_DATA_CACHE_64KB: 'y'
      CONFIG_ESP32S3_DATA_CACHE_LINE_64B: 'y'
      CONFIG_AUDIO_BOARD_CUSTOM: 'y'

psram:
  mode: octal
  speed: 80MHz

button:
  - platform: restart
    id: reboot_button
    name: 'ESP Reboot'
    entity_category: diagnostic
    icon: 'mdi:restart'
    disabled_by_default: true

  - platform: factory_reset
    id: factory_reset_button
    name: 'ESP Factory Reset'
    entity_category: diagnostic
    icon: 'mdi:factory'
    disabled_by_default: true
